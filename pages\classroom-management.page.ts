import { Page, Locator } from '@playwright/test';
import { DateFilter, Student_Success_Overview_objects } from "../test-objects/classroom_management";
import { StudentSuccessDetailData, readStudentSuccessDetailCSV } from "../test-objects/csv-utils";
import {
    classroomManagementLocators,
    classroomManagementRoleLocators,
    dayOptionsMap,
    defaultYearOptionsMap,
    skipCsvForQualifications,
    qualificationMapping,
    avoidQualificationValues,
    avoidQualificationValuesDetail,
    avoidCampusValues
} from './classroom-management.locators';
import * as allure from "allure-js-commons";

export class ClassroomManagementPage {
    public page: Page;
    private studentSuccessDetailData: StudentSuccessDetailData[] | null = null;
    public studentSuccessOverviewObjects: Student_Success_Overview_objects;

    constructor(page: Page) {
        this.page = page;
        this.studentSuccessOverviewObjects = new Student_Success_Overview_objects(page);
    }

    // Locator getters
    get classroomManagementMenu(): Locator {
        return this.page.getByText(classroomManagementRoleLocators.classroomManagementMenu.text);
    }

    get attendanceLink(): Locator {
        return this.page.locator(classroomManagementLocators.navigation.attendanceLink);
    }

    get studentSuccessOverviewLink(): Locator {
        return this.page.locator(classroomManagementLocators.navigation.studentSuccessOverviewLink);
    }

    get studentSuccessDetailLink(): Locator {
        return this.page.locator(classroomManagementLocators.navigation.studentSuccessDetailLink);
    }

    get timetablesLink(): Locator {
        return this.page.getByRole(classroomManagementRoleLocators.timetablesLink.role as 'link', {
            name: classroomManagementRoleLocators.timetablesLink.name
        });
    }

    get lateResubmissionReportLink(): Locator {
        return this.page.getByRole(classroomManagementRoleLocators.lateResubmissionReportLink.role as 'link', {
            name: classroomManagementRoleLocators.lateResubmissionReportLink.name
        });
    }

    get attendanceDetailLink(): Locator {
        return this.page.locator(classroomManagementLocators.navigation.attendanceDetailLink);
    }

    get presentFilterDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.attendance.presentFilterDropdown);
    }

    get attendanceApplyButton(): Locator {
        return this.page.locator(classroomManagementLocators.attendance.applyButton);
    }

    get attendanceFromDateInput(): Locator {
        return this.page.locator(classroomManagementLocators.attendance.fromDateInput);
    }

    get attendanceToDateInput(): Locator {
        return this.page.locator(classroomManagementLocators.attendance.toDateInput);
    }

    get dateApplyButton(): Locator {
        return this.page.locator(classroomManagementLocators.attendance.dateApplyButton);
    }

    get manageCampusButtons(): Locator {
        return this.page.locator(classroomManagementLocators.attendance.manageCampusButtons);
    }

    get complianceGrid(): Locator {
        return this.page.locator(classroomManagementLocators.attendance.complianceGrid);
    }

    get studentToggle(): Locator {
        return this.page.locator(classroomManagementLocators.attendance.studentToggle);
    }

    // Attendance Detail locators
    get attendanceDetailFromDateInput(): Locator {
        return this.page.locator(classroomManagementLocators.attendanceDetail.fromDateInput);
    }

    get attendanceDetailToDateInput(): Locator {
        return this.page.locator(classroomManagementLocators.attendanceDetail.toDateInput);
    }

    get campusDropdownArrow(): Locator {
        return this.page.locator(classroomManagementLocators.attendanceDetail.campusDropdownArrow);
    }

    get campusLabels(): Locator {
        return this.page.locator(classroomManagementLocators.attendanceDetail.campusLabels);
    }

    get attendanceDetailApplyButton(): Locator {
        return this.page.locator(classroomManagementLocators.attendanceDetail.applyButton);
    }

    get attendanceDetailDataGrid(): Locator {
        return this.page.locator(classroomManagementLocators.attendanceDetail.dataGrid);
    }

    // Student Success Overview locators
    get qualificationDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessOverview.qualificationDropdown);
    }

    get yearDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessOverview.yearDropdown);
    }

    get campusDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessOverview.campusDropdown);
    }

    get studentSuccessApplyFiltersButton(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessOverview.applyFiltersButton);
    }

    get studentSuccessDataGrid(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessOverview.dataGrid);
    }

    // Student Success Detail locators
    get collapseFilters(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessDetail.collapseFilters);
    }

    get detailQualificationDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessDetail.qualificationDropdown);
    }

    get detailYearDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessDetail.yearDropdown);
    }

    get detailCampusDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessDetail.campusDropdown);
    }

    get detailCourseDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessDetail.courseDropdown);
    }

    get detailApplyFiltersButton(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessDetail.applyFiltersButton);
    }

    get applyFiltersbutton(): Locator {
        return this.page.locator(classroomManagementLocators.studentSuccessDetail.applyFiltersButton);
    }

    get applyFiltersText(): Locator {
        return this.page.getByRole('link', { name: 'Apply filters' });
    }

    // Timetables locators
    get timetableCampusDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.timetables.campusDropdown);
    }

    get moduleCodeSpan(): Locator {
        return this.page.locator(classroomManagementLocators.timetables.moduleCodeSpan);
    }

    get timetableButton(): Locator {
        return this.page.locator(classroomManagementLocators.timetables.timetableButton);
    }

    get emptySessionBlock(): Locator {
        return this.page.locator(classroomManagementLocators.timetables.emptySessionBlock);
    }

    get sessionDetailsText(): Locator {
        return this.page.getByText(classroomManagementRoleLocators.sessionDetailsText.text);
    }

    get cancelButton(): Locator {
        return this.page.getByText(classroomManagementRoleLocators.cancelText.text);
    }

    // Late Resubmission locators
    get lateFromDateInput(): Locator {
        return this.page.locator(classroomManagementLocators.lateResubmission.fromDateInput);
    }

    get lateToDateInput(): Locator {
        return this.page.locator(classroomManagementLocators.lateResubmission.toDateInput);
    }

    get lateQualificationDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.lateResubmission.qualificationDropdown);
    }

    get lateAcademicYearDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.lateResubmission.academicYearDropdown);
    }

    get lateCalendarYearDropdown(): Locator {
        return this.page.locator(classroomManagementLocators.lateResubmission.calendarYearDropdown);
    }

    get lateDataTable(): Locator {
        return this.page.locator(classroomManagementLocators.lateResubmission.dataTable);
    }

    get briefsDataGrid(): Locator {
        return this.page.locator(classroomManagementLocators.briefs.dataGrid);
    }

    /**
     * Loads the CSV data for student success detail if not already loaded
     */
    private async loadStudentSuccessDetailData(): Promise<void> {
        if (!this.studentSuccessDetailData) {
            this.studentSuccessDetailData = await readStudentSuccessDetailCSV('data/student-success-detail.csv');
        }
    }

    // Action methods
    async navigateToAttendance(): Promise<void> {
        await this.classroomManagementMenu.click();
        await this.attendanceLink.click();
    }

    async checkAttendanceWithPresentFilter(): Promise<void> {
    try {
            const selectOptions = ['30 days', '60 days', '90 days'] as const;
            const randomSelection = Math.floor(Math.random() * selectOptions.length);
            const optionToSelect = selectOptions[randomSelection];
            const optionValue = dayOptionsMap[optionToSelect];

            await this.presentFilterDropdown.selectOption({ value: optionValue });
            console.log(`Selected ${optionToSelect} (value: ${optionValue})`);

            await this.attendanceApplyButton.click();
            await this.page.waitForTimeout(3000);

            const manageCampusButtons = await this.manageCampusButtons.all();
            if (manageCampusButtons.length === 0) {
                throw new Error('No manage campus buttons found');
            }

            const randomButton = Math.floor(Math.random() * manageCampusButtons.length);
            await manageCampusButtons[randomButton].click();
            await this.page.waitForTimeout(3000);
            await this.page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });

            await this.page.waitForTimeout(3000);
            const isVisible = await this.complianceGrid.isVisible();

            if (isVisible) {
                console.log("Present filter | Attendance data grid is displayed");
            } else {
                throw new Error("Present filter | Attendance data grid is not displayed");
            }

            await this.page.waitForTimeout(3000);
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in checkAttendanceWithPresentFilter:', error.message);
            } else {
                console.error('Error in checkAttendanceWithPresentFilter:', error);
            }
            throw error;
        }
    }

    async checkAttendanceWithDateFilter(fromDate?: Date | string, toDate?: Date | string): Promise<void> {
    try {
            const dateFilter = new DateFilter(fromDate, toDate);

            await this.page.waitForTimeout(3000);
            await this.attendanceFromDateInput.fill(dateFilter.getFromDateString());
            const fromDateScreenshot = await this.page.screenshot();
            await allure.attachment("FromDate", fromDateScreenshot, "image/png");

            await this.page.waitForTimeout(3000);
            await this.attendanceToDateInput.fill(dateFilter.getToDateString());
            const toDateScreenshot = await this.page.screenshot();
            await allure.attachment("ToDate", toDateScreenshot, "image/png");

            await this.dateApplyButton.click();
            await this.page.waitForTimeout(3000);

            const manageCampusButtons = await this.manageCampusButtons.all();
            if (manageCampusButtons.length === 0) {
                throw new Error('No manage campus buttons found');
            }

            const randomButton = Math.floor(Math.random() * manageCampusButtons.length);
            await manageCampusButtons[randomButton].click();
            await this.page.waitForTimeout(3000);
            await this.page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });

            const isVisible = await this.complianceGrid.isVisible();

            await this.page.waitForTimeout(3000);
            const gridScreenshot = await this.page.screenshot();
            await allure.attachment("AttendanceDataGrid", gridScreenshot, "image/png");

            if (isVisible) {
                console.log("From & To date | Attendance data grid is displayed");
            } else {
                throw new Error("From & To date | Attendance data grid is not displayed");
            }
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in checkAttendanceWithDateFilter:', error.message);
            } else {
                console.error('Error in checkAttendanceWithDateFilter:', error);
            }
            throw error;
        }
    }

    async toggleToStudentCompliance(): Promise<void> {
        await this.studentToggle.click();
        await this.page.waitForTimeout(4000);
    }

    async navigateToAttendanceDetail(): Promise<void> {
        const timeout = 30000; // 30 seconds

    try {
            // Wait for the element to be visible
            await this.attendanceDetailLink.waitFor({ state: 'visible', timeout });

            // Check if the element is enabled
            const isEnabled = await this.attendanceDetailLink.isEnabled();
            const isVisible = await this.attendanceDetailLink.isVisible();

            console.log(`Attendance Detail element: Visible - ${isVisible}, Enabled - ${isEnabled}`);

            if (isVisible && isEnabled) {
                await this.attendanceDetailLink.click();
                console.log('Clicked on "Attendance Detail"');
            } else {
                const errorMessage = `Attendance Detail element not interactive: Visible - ${isVisible}, Enabled - ${isEnabled}`;
                console.error(errorMessage);
                throw new Error(errorMessage);
            }
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error interacting with Attendance Detail element:', error.message);
            } else {
                console.error('Error interacting with Attendance Detail element:', error);
            }
            throw error; // Re-throw the error to fail the test if necessary
        }
    }

    /**
     * Applies date filters to the attendance detail view
     * @param fromDate - Optional start date (defaults to 30 days ago if not provided)
     * @param toDate - Optional end date (defaults to current date if not provided)
     */
    async applyAttendanceDetailFilters(fromDate?: Date | string, toDate?: Date | string): Promise<void> {
        const dateFilter = new DateFilter(fromDate, toDate);

        await this.page.waitForTimeout(2000);
        await this.attendanceDetailFromDateInput.fill(dateFilter.getFromDateString());
        await this.page.waitForTimeout(2000);
        await this.attendanceDetailToDateInput.fill(dateFilter.getToDateString());
    }

    async selectRandomCampusForAttendanceDetail(): Promise<void> {
    try {
            const dropdownArrow = await this.campusDropdownArrow.first();
            if (!dropdownArrow) {
                throw new Error('Dropdown arrow not found');
            }

            await dropdownArrow.click();

            const labels = await this.campusLabels.evaluateAll((elements: HTMLLabelElement[]) =>
                elements
                    .map(el => el.textContent?.trim() ?? '')
                    .filter(text => text !== '--Please Select--')
            );

            if (labels.length === 0) {
                throw new Error('No campus labels found');
            }

            const randomIndex = Math.floor(Math.random() * labels.length);
            const randomLabel = labels[randomIndex];

            await this.page.click(
                `.d-flex.single-mode > input[name="c31"] + label:has-text("${randomLabel}")`,
                {force: true}
            );

            console.log(`Selected Campus: ${randomLabel}`);
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in selectRandomCampusForAttendanceDetail:', error.message);
            } else {
                console.error('Error in selectRandomCampusForAttendanceDetail:', error);
            }
            throw error;
        }

        await this.attendanceDetailApplyButton.click();
    }

    async isAttendanceDetailDataGridDisplayed(): Promise<boolean> {
        await this.page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
        });
        // Wait for the grid to be available and try to get its visibility state
        try {
            await this.attendanceDetailDataGrid.waitFor({ state: 'visible' });
            return await this.attendanceDetailDataGrid.isVisible();
        } catch (error) {
            console.log('Waiting for data grid');
            return false;
        }
    }

    async isBriefsDataGridDisplayed(): Promise<boolean> {
        await this.page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
        });
        // Wait for the grid to be available and try to get its visibility state
        try {
            await this.briefsDataGrid.waitFor({ state: 'visible' });
            return await this.briefsDataGrid.isVisible();
        } catch (error) {
            console.log('Waiting for data grid');
            return false;
        }
    }

    async navigateToStudentSuccessOverview(): Promise<void> {
        await this.classroomManagementMenu.click();
        await this.studentSuccessOverviewLink.click();

        // Wait for the page to load completely
        await this.page.waitForLoadState('networkidle');

        // Wait for the qualification dropdown to be visible
        await this.qualificationDropdown.waitFor({ state: 'visible', timeout: 10000 });

        console.log('Successfully navigated to Student Success Overview page');
    }

    async navigateToStudentDetailOverview(): Promise<void> {
        await this.classroomManagementMenu.click();
        await this.studentSuccessDetailLink.click();
        await this.applyFiltersText.click();
    }

    // Helper methods for qualification selection
    async selectRandomQualification(): Promise<void> {
        await this.page.waitForTimeout(3000);
        try {
            const dropdown = this.qualificationDropdown;

            // Wait for the dropdown to be visible and loaded
            await dropdown.waitFor({ state: 'visible', timeout: 10000 });

            // Wait for options to be loaded
            await this.page.waitForFunction(() => {
                const select = document.querySelector('#c5') as HTMLSelectElement;
                return select && select.options.length > 1; // More than just the default option
            }, { timeout: 15000 });

            const options = await dropdown.locator('option').all();
            console.log(`Total options found: ${options.length}`);

            const validOptions = [];
            const allOptionValues = [];

            for (const option of options) {
                const value = await option.getAttribute('value');
                const text = await option.textContent();
                allOptionValues.push({ value, text });

                if (value && !avoidQualificationValues.includes(value)) {
                    validOptions.push(option);
                }
            }

            console.log('All available options:', allOptionValues);
            console.log(`Valid options after filtering: ${validOptions.length}`);
            console.log('Avoided values:', avoidQualificationValues);

            if (validOptions.length === 0) {
                console.error('No valid qualification options found after filtering');
                console.error('Available options:', allOptionValues);
                throw new Error('No valid qualification options found');
            }

            const randomIndex = Math.floor(Math.random() * validOptions.length);
            const selectedOption = validOptions[randomIndex];
            const value = await selectedOption.getAttribute('value');
            const text = await selectedOption.textContent();

            if (!value) {
                throw new Error('Selected option has no value');
            }

            await dropdown.selectOption({ value });
            console.log(`Selected Qualification: ${text} (value: ${value})`);
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in selectRandomQualification:', error.message);
            } else {
                console.error('Error in selectRandomQualification:', error);
            }
            throw error;
        }
    }

    async selectRandomQualificationForDetail(): Promise<void> {
        await this.page.waitForTimeout(3000);
        await this.collapseFilters.click();
    try {
            const dropdown = this.detailQualificationDropdown;
            const options = await dropdown.locator('option').all();
            const validOptions = [];

            for (const option of options) {
                const value = await option.getAttribute('value');
                if (value && !avoidQualificationValuesDetail.includes(value)) {
                    validOptions.push(option);
                }
            }

            if (validOptions.length === 0) {
                throw new Error('No valid qualification options found');
            }

            const randomIndex = Math.floor(Math.random() * validOptions.length);
            const selectedOption = validOptions[randomIndex];
            const value = await selectedOption.getAttribute('value');

            if (!value) {
                throw new Error('Selected option has no value');
            }

            await dropdown.selectOption({ value });
            console.log(`Selected Qualification: ${value}`);
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in selectRandomQualificationForDetail:', error.message);
            } else {
                console.error('Error in selectRandomQualificationForDetail:', error);
            }
            throw error;
        }
    }

    async selectRandomOption(dropdown: Locator, label: string): Promise<void> {
    try {
            const options = await dropdown.locator('option').all();
            const validOptions = [];

            for (const option of options) {
                const value = await option.getAttribute('value');
                if (value && value !== "" && value !== "0") {
                    validOptions.push(option);
                }
            }

            if (validOptions.length > 0) {
                const randomIndex = Math.floor(Math.random() * validOptions.length);
                const option = validOptions[randomIndex];
                const optionValue = await option.getAttribute('value');
                const optionLabel = await option.textContent();

                if (optionValue) {
                    await dropdown.selectOption({ value: optionValue });
                    console.log(`Selected ${label}: ${optionLabel?.trim() ?? ''} (value: ${optionValue})`);
                }
            } else {
                throw new Error(`No valid options available for ${label}`);
            }
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error(`Error selecting random option for ${label}:`, error.message);
            } else {
                console.error(`Error selecting random option for ${label}:`, error);
            }
            throw error;
        }
    }

    // async applyStudentSuccessOverviewFilters() {
    //     // Load the CSV data first
    //     await this.loadStudentSuccessDetailData();

    //     // Select a random qualification
    //     await this.selectRandomQualification();
    //     await this.page.waitForTimeout(3000);

    //     // Get the selected qualification text and value
    //     const qualificationDropdown = this.page.locator('#c5');
    //     const [selectedQualificationOption, selectedQualificationValue] = await qualificationDropdown.evaluate((select: HTMLSelectElement) => {
    //         const options = Array.from(select.options);
    //         const selectedOption = options.find(option => option.selected);
    //         return selectedOption ? [selectedOption.text, selectedOption.value] : [null, null];
    //     });

    //     if (!selectedQualificationOption) {
    //         throw new Error('No qualification selected');
    //     }

    //     console.log(`Selected qualification: ${selectedQualificationOption} (value: ${selectedQualificationValue})`);

    //     // List of qualification values that should use default year selection
    //     // These are qualifications that we know aren't in the CSV or won't work with the CSV approach
    //     const skipCsvForQualifications = ['25', '26', '27']; // Add more qualification values as needed

    //     if (skipCsvForQualifications.includes(selectedQualificationValue)) {
    //         console.log(`Qualification "${selectedQualificationOption}" (value: ${selectedQualificationValue}) is in the skip list. Using default year selection.`);

    //         // Default year options map for fallback
    //         const defaultYearOptionsMap: Record<string, string> = {
    //             '2023': '1',
    //             '2024': '2',
    //             '2025': '3',
    //             '2026': '4'
    //         };

    //         // Get the current year
    //         const currentYear = new Date().getFullYear().toString();
    //         const selectYear = this.page.locator('#c6');

    //         // Use the default map to get the correct option value for the current year
    //         const yearOptionValue = defaultYearOptionsMap[currentYear as keyof typeof defaultYearOptionsMap];
    //         if (yearOptionValue) {
    //             await selectYear.selectOption({value: yearOptionValue});
    //             console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
    //         } else {
    //             // Fallback to selecting by label if the current year is not in the map
    //             await selectYear.selectOption({label: currentYear});
    //             console.log(`Selected year by label: ${currentYear}`);
    //         }
    //     } else {
    //         // Get the current year
    //         const currentYear = new Date().getFullYear().toString();

    //         // Log available qualifications in the CSV for debugging
    //         console.log(`Available qualifications in CSV: ${this.studentSuccessDetailData?.map(entry => entry.Qualification).join(', ')}`);

    //         // Find the matching entry in the CSV data
    //         const matchingEntries = this.studentSuccessDetailData?.filter(entry => {
    //             const csvQual = entry.Qualification.trim();
    //             const selectedQual = selectedQualificationOption.trim();
    //             const yearMatch = entry['Calendar Year'] === currentYear;

    //             // Log detailed comparison for debugging
    //             if (csvQual.includes(selectedQual) || selectedQual.includes(csvQual)) {
    //                 console.log(`Partial match: CSV="${csvQual}", Selected="${selectedQual}", Year match=${yearMatch}`);
    //             }

    //             return csvQual === selectedQual && yearMatch;
    //         });

    //         if (!matchingEntries || matchingEntries.length === 0) {
    //             console.log(`No exact matching entry found for qualification "${selectedQualificationOption}" and year "${currentYear}"`);

    //             // Try a more flexible match (contains instead of exact match)
    //             const flexibleMatches = this.studentSuccessDetailData?.filter(entry =>
    //                 (entry.Qualification.trim().includes(selectedQualificationOption.trim()) ||
    //                  selectedQualificationOption.trim().includes(entry.Qualification.trim())) &&
    //                 entry['Calendar Year'] === currentYear
    //             );

    //             if (flexibleMatches && flexibleMatches.length > 0) {
    //                 // Use the first flexible match
    //                 const flexMatch = flexibleMatches[0];
    //                 console.log(`Found flexible match: ${JSON.stringify(flexMatch)}`);

    //                 const selectYear = this.page.locator('#c6');

    //                 // Log available year options for debugging
    //                 const yearOptions = await selectYear.evaluate((select: HTMLSelectElement) => {
    //                     return Array.from(select.options).map(option => ({
    //                         text: option.text,
    //                         value: option.value
    //                     }));
    //                 });
    //                 console.log(`Available year options: ${JSON.stringify(yearOptions)}`);
    //                 console.log(`Attempting to select year option with value: "${flexMatch['Year Option']}"`);

    //                 try {
    //                     await selectYear.selectOption({value: flexMatch['Year Option']});
    //                     console.log(`Successfully selected year option from flexible match: ${flexMatch['Year Option']}`);
    //                 } catch (error) {
    //                     console.error(`Error selecting year option: ${error}`);
    //                     console.log('Falling back to selecting by index...');

    //                     // Find the index of the option with a similar value
    //                     const optionIndex = yearOptions.findIndex(opt =>
    //                         opt.value === flexMatch['Year Option'] ||
    //                         opt.value.includes(flexMatch['Year Option']) ||
    //                         flexMatch['Year Option'].includes(opt.value)
    //                     );

    //                     if (optionIndex > 0) {
    //                         await selectYear.selectOption({index: optionIndex});
    //                         console.log(`Selected year option by index: ${optionIndex}`);
    //                     } else {
    //                         // Last resort: select by label using the calendar year
    //                         await selectYear.selectOption({label: flexMatch['Calendar Year']});
    //                         console.log(`Selected year option by label: ${flexMatch['Calendar Year']}`);
    //                     }
    //                 }
    //             } else {
    //                 console.log('No flexible match found. Falling back to default year option selection');

    //                 // Default year options map for fallback
    //                 const defaultYearOptionsMap: Record<string, string> = {
    //                     '2023': '1',
    //                     '2024': '2',
    //                     '2025': '3',
    //                     '2026': '4'
    //                 };

    //                 const selectYear = this.page.locator('#c6');

    //                 // Use the default map to get the correct option value for the current year
    //                 const yearOptionValue = defaultYearOptionsMap[currentYear as keyof typeof defaultYearOptionsMap];
    //                 if (yearOptionValue) {
    //                     await selectYear.selectOption({value: yearOptionValue});
    //                     console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
    //                 } else {
    //                     // Fallback to selecting by label if the current year is not in the map
    //                     await selectYear.selectOption({label: currentYear});
    //                     console.log(`Selected year by label: ${currentYear}`);
    //                 }
    //             }
    //         } else {
    //             // Use the year option from the CSV data
    //             const matchingEntry = matchingEntries[0];
    //             console.log(`Found exact matching entry: ${JSON.stringify(matchingEntry)}`);

    //             const selectYear = this.page.locator('#c6');

    //             // Log available year options for debugging
    //             const yearOptions = await selectYear.evaluate((select: HTMLSelectElement) => {
    //                 return Array.from(select.options).map(option => ({
    //                     text: option.text,
    //                     value: option.value
    //                 }));
    //             });
    //             console.log(`Available year options: ${JSON.stringify(yearOptions)}`);
    //             console.log(`Attempting to select year option with value: "${matchingEntry['Year Option']}"`);

    //             try {
    //                 await selectYear.selectOption({value: matchingEntry['Year Option']});
    //                 console.log(`Successfully selected year option: ${matchingEntry['Year Option']}`);
    //             } catch (error) {
    //                 console.error(`Error selecting year option: ${error}`);
    //                 console.log('Falling back to selecting by index...');

    //                 // Find the index of the option with a similar value
    //                 const optionIndex = yearOptions.findIndex(opt =>
    //                     opt.value === matchingEntry['Year Option'] ||
    //                     opt.value.includes(matchingEntry['Year Option']) ||
    //                     matchingEntry['Year Option'].includes(opt.value)
    //                 );

    //                 if (optionIndex > 0) {
    //                     await selectYear.selectOption({index: optionIndex});
    //                     console.log(`Selected year option by index: ${optionIndex}`);
    //                 } else {
    //                     // Last resort: select by label using the calendar year
    //                     await selectYear.selectOption({label: matchingEntry['Calendar Year']});
    //                     console.log(`Selected year option by label: ${matchingEntry['Calendar Year']}`);
    //                 }
    //             }
    //         }
    //     }

    //     await this.page.waitForTimeout(3000);

    //     const dropdownSelector = await this.page.$('select#c7');
    //     if (!dropdownSelector) {
    //         throw new Error('Dropdown selector not found');
    //     }
    //     const dropdownOptions = await dropdownSelector.$$eval('option', options => options.map(option => option.value));
    //     dropdownOptions.shift();
    //     const randomOptionIndex = dropdownOptions[Math.floor(Math.random() * dropdownOptions.length)];
    //     await dropdownSelector.selectOption({value: randomOptionIndex});
    //     await this.page.waitForTimeout(3000);
    // }

    async applyStudentSuccessDetailFilters() {
        // Load the CSV data first
        await this.loadStudentSuccessDetailData();

        // Select a random qualification
        await this.studentSuccessOverviewObjects.randomQualificationStudentSuccessDetail();
        await this.page.waitForTimeout(3000);

        // Get the selected qualification text and value
        const qualificationDropdown = this.page.locator('#c1');
        const [selectedQualificationOption, selectedQualificationValue] = await qualificationDropdown.evaluate((select: HTMLSelectElement) => {
            const options = Array.from(select.options);
            const selectedOption = options.find(option => option.selected);
            return selectedOption ? [selectedOption.text, selectedOption.value] : [null, null];
        });

        if (!selectedQualificationOption) {
            throw new Error('No qualification selected');
        }

        console.log(`Selected qualification: ${selectedQualificationOption} (value: ${selectedQualificationValue})`);

        // List of qualification values that should use default year selection
        // These are qualifications that we know aren't in the CSV or won't work with the CSV approach
        const skipCsvForQualifications = ['25', '26', '27']; // Add more qualification values as needed

        if (skipCsvForQualifications.includes(selectedQualificationValue)) {
            console.log(`Qualification "${selectedQualificationOption}" (value: ${selectedQualificationValue}) is in the skip list. Using default year selection.`);

            // Default year options map for fallback
            const defaultYearOptionsMap: Record<string, string> = {
                '2023': '1',
                '2024': '2',
                '2025': '3',
                '2026': '4'
            };

            // Get the current year
            const currentYear = new Date().getFullYear().toString();
            const selectYear = this.page.locator('#c2');

            // Use the default map to get the correct option value for the current year
            const yearOptionValue = defaultYearOptionsMap[currentYear as keyof typeof defaultYearOptionsMap];
            if (yearOptionValue) {
                await selectYear.selectOption({value: yearOptionValue});
                console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
            } else {
                // Fallback to selecting by label if the current year is not in the map
                await selectYear.selectOption({label: currentYear});
                console.log(`Selected year by label: ${currentYear}`);
            }
        } else {
            // Get the current year
            const currentYear = new Date().getFullYear().toString();

            // Log available qualifications in the CSV for debugging
            console.log(`Available qualifications in CSV: ${this.studentSuccessDetailData?.map(entry => entry.Qualification).join(', ')}`);

            // Find the matching entry in the CSV data
            const matchingEntries = this.studentSuccessDetailData?.filter(entry => {
                const csvQual = entry.Qualification.trim();
                const selectedQual = selectedQualificationOption.trim();
                const yearMatch = entry['Calendar Year'] === currentYear;

                // Log detailed comparison for debugging
                if (csvQual.includes(selectedQual) || selectedQual.includes(csvQual)) {
                    console.log(`Partial match: CSV="${csvQual}", Selected="${selectedQual}", Year match=${yearMatch}`);
                }

                return csvQual === selectedQual && yearMatch;
            });

            if (!matchingEntries || matchingEntries.length === 0) {
                console.log(`No exact matching entry found for qualification "${selectedQualificationOption}" and year "${currentYear}"`);

                // Try a more flexible match (contains instead of exact match)
                const flexibleMatches = this.studentSuccessDetailData?.filter(entry =>
                    (entry.Qualification.trim().includes(selectedQualificationOption.trim()) ||
                     selectedQualificationOption.trim().includes(entry.Qualification.trim())) &&
                    entry['Calendar Year'] === currentYear
                );

                if (flexibleMatches && flexibleMatches.length > 0) {
                    // Use the first flexible match
                    const flexMatch = flexibleMatches[0];
                    console.log(`Found flexible match: ${JSON.stringify(flexMatch)}`);

                    const selectYear = this.page.locator('#c2');

                    // Log available year options for debugging
                    const yearOptions = await selectYear.evaluate((select: HTMLSelectElement) => {
                        return Array.from(select.options).map(option => ({
                            text: option.text,
                            value: option.value
                        }));
                    });
                    console.log(`Available year options: ${JSON.stringify(yearOptions)}`);
                    console.log(`Attempting to select year option with value: "${flexMatch['Year Option']}"`);

                    try {
                        await selectYear.selectOption({value: flexMatch['Year Option']});
                        console.log(`Successfully selected year option from flexible match: ${flexMatch['Year Option']}`);
                    } catch (error) {
                        console.error(`Error selecting year option: ${error}`);
                        console.log('Falling back to selecting by index...');

                        // Find the index of the option with a similar value
                        const optionIndex = yearOptions.findIndex(opt =>
                            opt.value === flexMatch['Year Option'] ||
                            opt.value.includes(flexMatch['Year Option']) ||
                            flexMatch['Year Option'].includes(opt.value)
                        );

                        if (optionIndex > 0) {
                            await selectYear.selectOption({index: optionIndex});
                            console.log(`Selected year option by index: ${optionIndex}`);
                        } else {
                            // Last resort: select by label using the calendar year
                            await selectYear.selectOption({label: flexMatch['Calendar Year']});
                            console.log(`Selected year option by label: ${flexMatch['Calendar Year']}`);
                        }
                    }
                } else {
                    console.log('No flexible match found. Falling back to default year option selection');

                    // Default year options map for fallback
                    const defaultYearOptionsMap: Record<string, string> = {
                        '2023': '1',
                        '2024': '2',
                        '2025': '3',
                        '2026': '4'
                    };

                    const selectYear = this.page.locator('#c2');

                    // Use the default map to get the correct option value for the current year
                    const yearOptionValue = defaultYearOptionsMap[currentYear as keyof typeof defaultYearOptionsMap];
                    if (yearOptionValue) {
                        await selectYear.selectOption({value: yearOptionValue});
                        console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
                    } else {
                        // Fallback to selecting by label if the current year is not in the map
                        await selectYear.selectOption({label: currentYear});
                        console.log(`Selected year by label: ${currentYear}`);
                    }
                }
            } else {
                // Use the year option from the CSV data
                const matchingEntry = matchingEntries[0];
                console.log(`Found exact matching entry: ${JSON.stringify(matchingEntry)}`);

                const selectYear = this.page.locator('#c2');

                // Log available year options for debugging
                const yearOptions = await selectYear.evaluate((select: HTMLSelectElement) => {
                    return Array.from(select.options).map(option => ({
                        text: option.text,
                        value: option.value
                    }));
                });
                console.log(`Available year options: ${JSON.stringify(yearOptions)}`);
                console.log(`Attempting to select year option with value: "${matchingEntry['Year Option']}"`);

                try {
                    await selectYear.selectOption({value: matchingEntry['Year Option']});
                    console.log(`Successfully selected year option: ${matchingEntry['Year Option']}`);
                } catch (error) {
                    console.error(`Error selecting year option: ${error}`);
                    console.log('Falling back to selecting by index...');

                    // Find the index of the option with a similar value
                    const optionIndex = yearOptions.findIndex(opt =>
                        opt.value === matchingEntry['Year Option'] ||
                        opt.value.includes(matchingEntry['Year Option']) ||
                        matchingEntry['Year Option'].includes(opt.value)
                    );

                    if (optionIndex > 0) {
                        await selectYear.selectOption({index: optionIndex});
                        console.log(`Selected year option by index: ${optionIndex}`);
                    } else {
                        // Last resort: select by label using the calendar year
                        await selectYear.selectOption({label: matchingEntry['Calendar Year']});
                        console.log(`Selected year option by label: ${matchingEntry['Calendar Year']}`);
                    }
                }
            }
        }

        await this.page.waitForTimeout(3000);

        const dropdownSelector = await this.page.$('select#c3');
        if (!dropdownSelector) {
            throw new Error('Dropdown selector not found');
        }
        const dropdownOptions = await dropdownSelector.$$eval('option', options => options.map(option => option.value));
        dropdownOptions.shift();
        const randomOptionIndex = dropdownOptions[Math.floor(Math.random() * dropdownOptions.length)];
        await dropdownSelector.selectOption({value: randomOptionIndex});
        await this.page.waitForTimeout(3000);

        const dropdownCourseSelector = await this.page.$('select#c4');
        if (!dropdownCourseSelector) {
            throw new Error('Dropdown course selector not found');
        }
        const dropdownCourseOptions = await dropdownCourseSelector.$$eval('option', options => options.map(option => option.value));
        dropdownCourseOptions.shift();
        const randomCourseOptionIndex = dropdownCourseOptions[Math.floor(Math.random() * dropdownCourseOptions.length)];
        await dropdownCourseSelector.selectOption({value: randomCourseOptionIndex});
        await this.page.waitForTimeout(3000);

        const applyFiltersButton = await this.page.$('#c7');
        await applyFiltersButton?.click();
        await this.page.waitForTimeout(5000);
    }

    // async applyStudentSuccessOverviewFiltersAndCheckDataGrid() {
    //     const applyFiltersButton = await this.page.$('#c12');
    //     await applyFiltersButton?.click();
    //     await this.page.waitForTimeout(5000);

    //     const dataGridSuccess = this.page.locator('#c3 table tbody');
    //     return dataGridSuccess.isVisible();
    // }

    // Timetable methods
    async navigateToTimetables(): Promise<void> {
        await this.classroomManagementMenu.click();
        await this.timetablesLink.click();
        await this.page.waitForLoadState('networkidle');
    }

    async selectRandomCampusForTimetable(): Promise<void> {
    try {
            const dropdown = this.timetableCampusDropdown;
            const options = await dropdown.locator('option').all();
            const validOptions = [];

            for (const option of options) {
                const value = await option.getAttribute('value');
                if (value && !avoidCampusValues.includes(value)) {
                    validOptions.push(option);
                }
            }

            if (validOptions.length === 0) {
                throw new Error('No valid campus options found');
            }

            const randomIndex = Math.floor(Math.random() * validOptions.length);
            const selectedOption = validOptions[randomIndex];
            const value = await selectedOption.getAttribute('value');

            if (!value) {
                throw new Error('Selected option has no value');
            }

            await dropdown.selectOption({ value });
            console.log(`Selected Campus: ${value}`);
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in selectRandomCampusForTimetable:', error.message);
            } else {
                console.error('Error in selectRandomCampusForTimetable:', error);
            }
            throw error;
        }
        await this.page.waitForTimeout(6000);
    }

    async selectRandomSession(): Promise<boolean> {
    try {
            let moduleFound = false;
            let attempts = 0;
            const maxAttempts = 10;

            while (!moduleFound && attempts < maxAttempts) {
                const moduleCodeElement = await this.moduleCodeSpan.first();
                if (await moduleCodeElement.isVisible()) {
                    const moduleCode = await moduleCodeElement.textContent();
                    console.log(`Module Code: ${moduleCode}`);
                    await this.page.waitForTimeout(3000);
                    await moduleCodeElement.click();
                    moduleFound = true;
                } else {
                    await this.timetableButton.click({ force: true });
                    await this.page.waitForTimeout(3000);
                    attempts++;
                }
            }

            if (!moduleFound) {
                throw new Error('Failed to find module after maximum attempts');
            }
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in selectRandomSession:', error.message);
            } else {
                console.error('Error in selectRandomSession:', error);
            }
            throw error;
        }
        await this.page.waitForTimeout(3000);
        return await this.sessionDetailsText.isVisible();
    }

    async closeSessionDetails(): Promise<void> {
        await this.page.getByLabel('Session Details').getByText('Cancel').click();
        await this.page.waitForTimeout(3000);
    }

    async createBlankSession(): Promise<boolean> {
    try {
            const emptySessionBlock = await this.emptySessionBlock.first();
            if (!(await emptySessionBlock.isVisible())) {
                throw new Error('No empty session block found');
            }

            await emptySessionBlock.hover();
            await this.page.waitForTimeout(1000);
            await emptySessionBlock.click();
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in createBlankSession:', error.message);
            } else {
                console.error('Error in createBlankSession:', error);
            }
            throw error;
        }
        await this.page.waitForTimeout(3000);
        return await this.sessionDetailsText.isVisible();
    }

    // Late Resubmission methods
    async navigateToLateResubmissionReport(): Promise<void> {
        await this.classroomManagementMenu.click();
        await this.lateResubmissionReportLink.click();
        await this.page.waitForLoadState('networkidle');
    }

    async dateFilter(fromDate?: Date | string, toDate?: Date | string): Promise<void> {
    try {
            const dateFilter = new DateFilter(fromDate, toDate);

            await this.page.waitForTimeout(3000);
            await this.lateFromDateInput.fill(dateFilter.getFromDateString());
            const fromDateScreenshot = await this.page.screenshot();
            await allure.attachment("FromDate", fromDateScreenshot, "image/png");

            await this.page.waitForTimeout(3000);
            await this.lateToDateInput.fill(dateFilter.getToDateString());
            const toDateScreenshot = await this.page.screenshot();
            await allure.attachment("ToDate", toDateScreenshot, "image/png");

            await this.page.waitForTimeout(2000);
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in dateFilter:', error.message);
            } else {
                console.error('Error in dateFilter:', error);
            }
            throw error;
        }
    }

    async selectCalendarYear(): Promise<void> {
    try {
            // Always select 2024 (value: 1) since that's the only year we're testing with
            await this.lateCalendarYearDropdown.selectOption({ value: '1' });
            await this.page.waitForTimeout(2000);
            console.log('Selected Calendar Year 2024 (value: 1)');
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in selectCalendarYear:', error.message);
            } else {
                console.error('Error in selectCalendarYear:', error);
            }
            throw error;
        }
    }

    async getLateResubmissionData() {
        const row = this.lateDataTable.first();
        const totalStudentsPassed = await row.locator('td:nth-child(6)').textContent();
        const totalStudentsPassedBeforeLate = await row.locator('td:nth-child(4)').textContent();
        const averageCourseMarkAfterLate = await row.locator('td:nth-child(7)').textContent();
        const averageCourseMarkBeforeLate = await row.locator('td:nth-child(5)').textContent();
        return { totalStudentsPassed, totalStudentsPassedBeforeLate, averageCourseMarkAfterLate, averageCourseMarkBeforeLate };
    }

    async applyStudentSuccessOverviewFilters(): Promise<void> {
        try {
            // Load the CSV data first
            await this.loadStudentSuccessDetailData();

            // Wait for the page to be fully loaded
            await this.page.waitForLoadState('networkidle');

            // Ensure the qualification dropdown is visible and loaded
            await this.qualificationDropdown.waitFor({ state: 'visible', timeout: 10000 });

            console.log('Starting qualification selection for Student Success Overview');

            // Select a random qualification
            await this.selectRandomQualification();
            await this.page.waitForTimeout(3000);

            // Get the selected qualification text and value
            const qualificationDropdown = this.qualificationDropdown;
            const [selectedQualificationOption, selectedQualificationValue] = await qualificationDropdown.evaluate((select: HTMLSelectElement) => {
                const options = Array.from(select.options);
                const selectedOption = options.find(option => option.selected);
                return selectedOption ? [selectedOption.text, selectedOption.value] : [null, null];
            });

            if (!selectedQualificationOption) {
                throw new Error('No qualification selected');
            }

        console.log(`Selected qualification: ${selectedQualificationOption} (value: ${selectedQualificationValue})`);

        // List of qualification values that should use default year selection
        if (skipCsvForQualifications.includes(selectedQualificationValue)) {
            console.log(`Qualification "${selectedQualificationOption}" (value: ${selectedQualificationValue}) is in the skip list. Using default year selection.`);

            // Get the current year
            const currentYear = new Date().getFullYear().toString();
            const yearOptionValue = defaultYearOptionsMap[currentYear] || '2';
            await this.yearDropdown.selectOption({ value: yearOptionValue });
            console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
        } else {
            // Get the current year
            const currentYear = new Date().getFullYear().toString();

            // Log available qualifications in the CSV for debugging
            console.log(`Available qualifications in CSV: ${this.studentSuccessDetailData?.map(entry => entry.Qualification).join(', ')}`);

            // Find the matching entry in the CSV data
            const matchingEntries = this.studentSuccessDetailData?.filter(entry => {
                const csvQual = entry.Qualification.trim();
                const selectedQual = selectedQualificationOption.trim();
                const yearMatch = entry['Calendar Year'] === currentYear;

                // Log detailed comparison for debugging
                if (csvQual.includes(selectedQual) || selectedQual.includes(csvQual)) {
                    console.log(`Partial match: CSV="${csvQual}", Selected="${selectedQual}", Year match=${yearMatch}`);
                }

                return csvQual === selectedQual && yearMatch;
            });

            if (!matchingEntries || matchingEntries.length === 0) {
                console.log(`No exact matching entry found for qualification "${selectedQualificationOption}" and year "${currentYear}"`);

                // Try a more flexible match (contains instead of exact match)
                const flexibleMatches = this.studentSuccessDetailData?.filter(entry =>
                    (entry.Qualification.trim().includes(selectedQualificationOption.trim()) ||
                     selectedQualificationOption.trim().includes(entry.Qualification.trim())) &&
                    entry['Calendar Year'] === currentYear
                );

                if (flexibleMatches && flexibleMatches.length > 0) {
                    // Use the first flexible match
                    const flexMatch = flexibleMatches[0];
                    console.log(`Found flexible match: ${JSON.stringify(flexMatch)}`);

                    // Log available year options for debugging
                    const yearOptions = await this.yearDropdown.evaluate((select: HTMLSelectElement) => {
                        return Array.from(select.options).map(option => ({
                            text: option.text,
                            value: option.value
                        }));
                    });

                    console.log(`Available year options: ${JSON.stringify(yearOptions)}`);

                    // Try to select the year option from the CSV
                    if (flexMatch['Year Option']) {
                        const optionIndex = yearOptions.findIndex(opt =>
                            opt.value === flexMatch['Year Option'] ||
                            opt.value.includes(flexMatch['Year Option']) ||
                            flexMatch['Year Option'].includes(opt.value)
                        );

                        if (optionIndex > 0) {
                            await this.yearDropdown.selectOption({index: optionIndex});
                            console.log(`Selected year option by index: ${optionIndex}`);
                        } else {
                            // Last resort: select by label using the calendar year
                            await this.yearDropdown.selectOption({label: flexMatch['Calendar Year']});
                            console.log(`Selected year option by label: ${flexMatch['Calendar Year']}`);
                        }
                    }
                } else {
                    console.log('No flexible match found. Falling back to default year option selection');

                    // Get the current year
                    const currentYear = new Date().getFullYear().toString();
                    const yearOptionValue = defaultYearOptionsMap[currentYear] || '2';
                    await this.yearDropdown.selectOption({value: yearOptionValue});
                    console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
                }
            } else {
                // Use the year option from the CSV data
                const matchingEntry = matchingEntries[0];
                console.log(`Found exact matching entry: ${JSON.stringify(matchingEntry)}`);

                // Log available year options for debugging
                const yearOptions = await this.yearDropdown.evaluate((select: HTMLSelectElement) => {
                    return Array.from(select.options).map(option => ({
                        text: option.text,
                        value: option.value
                    }));
                });

                console.log(`Available year options: ${JSON.stringify(yearOptions)}`);

                // Try to select the year option from the CSV
                if (matchingEntry['Year Option']) {
                    const optionIndex = yearOptions.findIndex(opt =>
                        opt.value === matchingEntry['Year Option'] ||
                        opt.value.includes(matchingEntry['Year Option']) ||
                        matchingEntry['Year Option'].includes(opt.value)
                    );

                    if (optionIndex > 0) {
                        await this.yearDropdown.selectOption({index: optionIndex});
                        console.log(`Selected year option by index: ${optionIndex}`);
                    } else {
                        // Last resort: select by label using the calendar year
                        await this.yearDropdown.selectOption({label: matchingEntry['Calendar Year']});
                        console.log(`Selected year option by label: ${matchingEntry['Calendar Year']}`);
                    }
                }
            }
        }

        await this.page.waitForTimeout(3000);
        await this.selectRandomOption(this.campusDropdown, 'Campus');
        await this.page.waitForTimeout(3000);
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in applyStudentSuccessOverviewFilters:', error.message);
                console.error('Stack trace:', error.stack);
            } else {
                console.error('Error in applyStudentSuccessOverviewFilters:', error);
            }
            throw error;
        }
    }

    // async applyStudentSuccessDetailFilters(): Promise<void> {
    //     await this.loadStudentSuccessDetailData();
    //     await this.selectRandomQualificationForDetail();
    //     await this.page.waitForTimeout(3000);

    //     // Apply year selection logic (simplified version)
    //     const currentYear = new Date().getFullYear().toString();
    //     const yearOptionValue = defaultYearOptionsMap[currentYear] || '2';
    //     await this.detailYearDropdown.selectOption({ value: yearOptionValue });
    //     console.log(`Selected year option: ${yearOptionValue} for year ${currentYear}`);

    //     await this.page.waitForTimeout(3000);
    //     await this.selectRandomOption(this.detailCampusDropdown, 'Campus');
    //     await this.page.waitForTimeout(3000);
    //     await this.selectRandomOption(this.detailCourseDropdown, 'Course');
    //     await this.page.waitForTimeout(3000);

    //     await this.detailApplyFiltersButton.click();
    //     await this.page.waitForTimeout(5000);
    // }

    async applyStudentSuccessOverviewFiltersAndCheckDataGrid(): Promise<boolean> {
        await this.studentSuccessApplyFiltersButton.click();
        await this.page.waitForTimeout(5000);
        return await this.studentSuccessDataGrid.isVisible();
    }

    // Late resubmission qualification and academic year methods
    async randomLateQualification(): Promise<void> {
    try {
            await this.loadStudentSuccessDetailData();
            // Simplified: just select a random valid qualification
            await this.selectRandomOption(this.lateQualificationDropdown, 'Late Qualification');
            await this.page.waitForTimeout(2000);
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in randomLateQualification:', error.message);
            } else {
                console.error('Error in randomLateQualification:', error);
            }
            throw error;
        }
    }

    async randomAcademicYear(): Promise<void> {
    try {
            // Simplified: select a random academic year (1-4)
            const academicYearValue = Math.floor(Math.random() * 4).toString();
            await this.lateAcademicYearDropdown.selectOption({ value: academicYearValue });
            await this.page.waitForTimeout(2000);
            console.log(`Selected Academic Year: ${parseInt(academicYearValue) + 1} (value: ${academicYearValue})`);
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in randomAcademicYear:', error.message);
            } else {
                console.error('Error in randomAcademicYear:', error);
            }
            throw error;
        }
    }
}
