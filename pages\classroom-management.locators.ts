/**
 * Classroom Management module locators
 * Contains all selectors and locators for the Classroom Management page
 */

export const classroomManagementLocators = {
  // Navigation
  navigation: {
    classroomManagementMenu: 'text=Classroom Management',
    attendanceLink: '#ClassroomManagement_collapse a:has-text("Attendance")',
    studentSuccessOverviewLink: 'text=Student Success Overview',
    studentSuccessDetailLink: 'text=Student Success Detail',
    timetablesLink: 'link[name="Timetables"]',
    lateResubmissionReportLink: 'link[name="Late/Resubmission Report"]',
    attendanceDetailLink: 'text=Attendance Detail'
  },

  // Attendance filters
  attendance: {
    presentFilterDropdown: '#c6',
    applyButton: '#c7',
    fromDateInput: '#c8',
    toDateInput: '#c9',
    dateApplyButton: '#c11',
    manageCampusButtons: '.btn-success',
    complianceGrid: '#ComplianceGrid table tbody',
    studentToggle: '//div[@id=\'AttendanceOverview\']/div/div[1]/div/span/div/label/span'
  },

  // Attendance Detail
  attendanceDetail: {
    fromDateInput: '#c29',
    toDateInput: '#c30',
    campusDropdownArrow: '.customselect-dropdown > span',
    campusLabels: '.d-flex.single-mode > input[name="c31"] + label',
    applyButton: '#c34',
    dataGrid: '#c41_dataGrid table tbody'
  },

  // Student Success Overview
  studentSuccessOverview: {
    qualificationDropdown: '#c9',
    yearDropdown: '#c10',
    courseDropdown: '#c11',
    campusDropdown: '#c14',
    applyFiltersButton: '#c16',
    dataGrid: 'table tbody tr row'
  },


  // Student Success Detail
  studentSuccessDetail: {
    collapseFilters: '#collapseFilters',
    qualificationDropdown: '#c1',
    yearDropdown: '#c2',
    campusDropdown: '#c3',
    courseDropdown: '#c4',
    applyFiltersButton: '#c7',
    // applyFiltersText: 'text=Apply filters'
  },

  // Timetables
  timetables: {
    campusDropdown: '#c1',
    moduleCodeSpan: 'span.ModuleCode',
    timetableButton: 'button[onclick*="TimetableManagementForm"]',
    emptySessionBlock: '.EmptySessionBlock',
    sessionDetailsText: 'text=Session Details',
    cancelButton: 'text=Cancel'
  },

  // Late Resubmission Report
  lateResubmission: {
    fromDateInput: '#c2',
    toDateInput: '#c3',
    qualificationDropdown: '#c4',
    academicYearDropdown: '#c5',
    calendarYearDropdown: '#c6',
    dataTable: 'table tbody tr'
  },

  // Briefs
  briefs: {
    dataGrid: '#Briefs table tbody'
  }
};

/**
 * Classroom Management page role-based locators for better accessibility
 */
export const classroomManagementRoleLocators = {
  classroomManagementMenu: { text: 'Classroom Management' },
  timetablesLink: { role: 'link', name: 'Timetables' },
  lateResubmissionReportLink: { role: 'link', name: 'Late/Resubmission Report' },
  sessionDetailsText: { text: 'Session Details' },
  cancelText: { text: 'Cancel' }
};

/**
 * Day options mapping for attendance filters
 */
export const dayOptionsMap = {
  '10 days': '0',
  '30 days': '1',
  '60 days': '2', 
  '90 days': '3'
};

/**
 * Default year options mapping
 */
export const defaultYearOptionsMap: Record<string, string> = {
  '2023': '1',
  '2024': '2',
  '2025': '3',
  '2026': '4'
};

/**
 * Qualification values that should skip CSV lookup
 */
export const skipCsvForQualifications = ['25', '26', '27'];

/**
 * Qualification mapping for late resubmission report
 */
export const qualificationMapping: { [key: string]: string } = {
  'Bachelor  of  Design (110828)': '6',
  'Advanced Diploma in User Experience Design': '3',
  'Bachelor of Arts Honours in Design': '7',
  'Bachelor of Arts in Digital Marketing and Communication': '9',
  'Bachelor of Design in Interior Design': '11',
  'Diploma in Graphic Design': '12',
  'Diploma in Interior Design': '13',
  'Higher Certificate in Architectural Technology': '22',
  'Higher Certificate in Design Techniques': '24',
  'Higher Certificate in Fashion Design': '25'
};

/**
 * Values to avoid when selecting qualifications
 */
export const avoidQualificationValues = ['0', '1', '4', '5', '14', '15', '16', '17', '18', '19', '28', '29'];
export const avoidQualificationValuesDetail = ['0', '1', '4', '5', '14', '15', '16', '17', '18', '19', '28', '29', '30'];

/**
 * Values to avoid when selecting campus for timetables
 */
export const avoidCampusValues = ['0', '2'];
